# OPS Admin API - Next.js Integration Documentation

## Overview
Complete API documentation for integrating the OPS Admin API with Next.js applications. This API manages Kubernetes operations, clusters, deployments, jobs, and user management using hexagonal architecture.

## Base Configuration

### Base URL
```typescript
const API_BASE_URL = 'http://localhost:8080/api/v1'
```

### Authentication
Include JWT token in all protected endpoints:
```typescript
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
}
```

## TypeScript Types

### Base Response Types
```typescript
interface ApiResponse<T = any> {
  status: boolean
  message: string
  data: T | null
}

interface BaseEntity {
  id: number
  created_at: string
  updated_at: string
}
```

### User Types
```typescript
interface User extends BaseEntity {
  name: string
  email: string
  user_type?: UserType
}

interface UserType extends BaseEntity {
  name: string
  description: string
}

interface RegisterRequest {
  name: string
  email: string
  password: string // min 6 characters
}

interface LoginRequest {
  email: string
  password: string
}

interface ChangePasswordRequest {
  current_password: string
  new_password: string // min 6 characters
}

interface UpdateUserRequest {
  name: string
  email: string
}

interface AuthResponse {
  token: string
  user: User
}
```

### Workspace Types
```typescript
interface Workspace extends BaseEntity {
  name: string
  description: string
  user?: User
  clusters?: Cluster[]
}

interface CreateWorkspaceRequest {
  name: string // min 2, max 100 characters
  description?: string
  user_id: number
}

interface UpdateWorkspaceRequest {
  name: string // min 2, max 100 characters
  description?: string
}
```

### Job Types
```typescript
interface Job extends BaseEntity {
  name: string
  description: string
  event_id?: number
  job_status?: JobStatus
  job_action?: JobAction
  user?: User
}

interface JobStatus extends BaseEntity {
  name: string
  description: string
  color?: string
}

interface JobAction extends BaseEntity {
  name: string
  description: string
}

interface CreateJobRequest {
  name: string // min 2, max 100 characters
  description?: string // max 500 characters
  job_status_id: number
  job_action_id: number
  event_id?: number
}

interface UpdateJobRequest {
  name: string // min 2, max 100 characters
  description?: string // max 500 characters
  job_status_id: number
  job_action_id: number
  event_id?: number
}
```

### Cluster Types
```typescript
interface Cluster extends BaseEntity {
  name: string
  description: string
  config: string
  workspace?: Workspace
  namespaces?: Namespace[]
}

interface CreateClusterRequest {
  name: string
  description?: string
  config: string
  workspace_id: number
}

interface UpdateClusterRequest {
  name: string
  description?: string
  config: string
}
```

### Deployment Types
```typescript
interface Deployment extends BaseEntity {
  name: string
  description: string
  image: string
  replicas: number
  namespace?: Namespace
  environment?: Environment
}

interface CreateDeploymentRequest {
  name: string
  description?: string
  image: string
  replicas: number
  namespace_id: number
  environment_id: number
}
```

### Service Types
```typescript
interface Service extends BaseEntity {
  name: string
  port: string
  target_port: string
  type: string
  cluster_ip?: string
  external_ip?: string
  namespace_id: number
  deployment_id: number
}

interface CreateServiceRequest {
  name: string // min 2, max 100 characters
  port: string // port number as string
  target_port: string // target port number as string
  type: string // ClusterIP, NodePort, LoadBalancer
  cluster_ip?: string // optional cluster IP
  external_ip?: string // optional external IP
  namespace_id: number
  deployment_id: number
}

interface UpdateServiceRequest {
  name: string // min 2, max 100 characters
  port: string // port number as string
  target_port: string // target port number as string
  type: string // ClusterIP, NodePort, LoadBalancer
  cluster_ip?: string // optional cluster IP
  external_ip?: string // optional external IP
  namespace_id: number
  deployment_id: number
}
```

## API Endpoints

### Health Check
```typescript
// GET /health
async function checkHealth(): Promise<ApiResponse<{ status: string }>> {
  const response = await fetch('/health')
  return response.json()
}
```

### Authentication Endpoints

#### Register User
```typescript
// POST /api/v1/auth/register
async function registerUser(data: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
  const response = await fetch(`${API_BASE_URL}/auth/register`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

#### Login User
```typescript
// POST /api/v1/auth/login
async function loginUser(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
  const response = await fetch(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

### User Management Endpoints

#### Get User Profile
```typescript
// GET /api/v1/users/profile
async function getUserProfile(token: string): Promise<ApiResponse<User>> {
  const response = await fetch(`${API_BASE_URL}/users/profile`, {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

#### Update User Profile
```typescript
// PUT /api/v1/users/profile
async function updateUserProfile(token: string, data: UpdateUserRequest): Promise<ApiResponse<User>> {
  const response = await fetch(`${API_BASE_URL}/users/profile`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

#### Change Password
```typescript
// PUT /api/v1/users/change-password
async function changePassword(token: string, data: ChangePasswordRequest): Promise<ApiResponse<null>> {
  const response = await fetch(`${API_BASE_URL}/users/change-password`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

#### Get All Users (Admin Only)
```typescript
// GET /api/v1/users
async function getAllUsers(token: string): Promise<ApiResponse<User[]>> {
  const response = await fetch(`${API_BASE_URL}/users`, {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

#### Create User (Admin Only)
```typescript
// POST /api/v1/users
async function createUser(token: string, data: CreateUserRequest): Promise<ApiResponse<User>> {
  const response = await fetch(`${API_BASE_URL}/users`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

#### Delete User (Admin Only)
```typescript
// DELETE /api/v1/users/:id
async function deleteUser(token: string, userId: number): Promise<ApiResponse<null>> {
  const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
    method: 'DELETE',
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

### Workspace Management Endpoints

#### Get All Workspaces
```typescript
// GET /api/v1/workspaces
async function getWorkspaces(token: string): Promise<ApiResponse<Workspace[]>> {
  const response = await fetch(`${API_BASE_URL}/workspaces`, {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

#### Create Workspace
```typescript
// POST /api/v1/workspaces
async function createWorkspace(token: string, data: CreateWorkspaceRequest): Promise<ApiResponse<Workspace>> {
  const response = await fetch(`${API_BASE_URL}/workspaces`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

#### Get Workspace by ID
```typescript
// GET /api/v1/workspaces/:id
async function getWorkspaceById(token: string, workspaceId: number): Promise<ApiResponse<Workspace>> {
  const response = await fetch(`${API_BASE_URL}/workspaces/${workspaceId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

#### Update Workspace
```typescript
// PUT /api/v1/workspaces/:id
async function updateWorkspace(token: string, workspaceId: number, data: UpdateWorkspaceRequest): Promise<ApiResponse<Workspace>> {
  const response = await fetch(`${API_BASE_URL}/workspaces/${workspaceId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

#### Delete Workspace
```typescript
// DELETE /api/v1/workspaces/:id
async function deleteWorkspace(token: string, workspaceId: number): Promise<ApiResponse<null>> {
  const response = await fetch(`${API_BASE_URL}/workspaces/${workspaceId}`, {
    method: 'DELETE',
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

### Job Management Endpoints

#### Get All Jobs
```typescript
// GET /api/v1/jobs
async function getAllJobs(token: string): Promise<ApiResponse<Job[]>> {
  const response = await fetch(`${API_BASE_URL}/jobs`, {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

#### Get My Jobs
```typescript
// GET /api/v1/jobs/my
async function getMyJobs(token: string): Promise<ApiResponse<Job[]>> {
  const response = await fetch(`${API_BASE_URL}/jobs/my`, {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

#### Create Job
```typescript
// POST /api/v1/jobs
async function createJob(token: string, data: CreateJobRequest): Promise<ApiResponse<Job>> {
  const response = await fetch(`${API_BASE_URL}/jobs`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

#### Get Job by ID
```typescript
// GET /api/v1/jobs/:id
async function getJobById(token: string, jobId: number): Promise<ApiResponse<Job>> {
  const response = await fetch(`${API_BASE_URL}/jobs/${jobId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

#### Update Job
```typescript
// PUT /api/v1/jobs/:id
async function updateJob(token: string, jobId: number, data: UpdateJobRequest): Promise<ApiResponse<Job>> {
  const response = await fetch(`${API_BASE_URL}/jobs/${jobId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

### Service Management Endpoints

#### Get All Services
```typescript
// GET /api/v1/services
async function getAllServices(token: string, filters?: {
  namespace_id?: number
  deployment_id?: number
  name?: string
  type?: string
}): Promise<ApiResponse<Service[]>> {
  const searchParams = new URLSearchParams()

  if (filters?.namespace_id) {
    searchParams.append('namespace_id', filters.namespace_id.toString())
  }
  if (filters?.deployment_id) {
    searchParams.append('deployment_id', filters.deployment_id.toString())
  }
  if (filters?.name) {
    searchParams.append('name', filters.name)
  }
  if (filters?.type) {
    searchParams.append('type', filters.type)
  }

  const url = `${API_BASE_URL}/services${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
  const response = await fetch(url, {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

#### Create Service
```typescript
// POST /api/v1/services
async function createService(token: string, data: CreateServiceRequest): Promise<ApiResponse<Service>> {
  const response = await fetch(`${API_BASE_URL}/services`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

#### Get Service by ID
```typescript
// GET /api/v1/services/:id
async function getServiceById(token: string, serviceId: number): Promise<ApiResponse<Service>> {
  const response = await fetch(`${API_BASE_URL}/services/${serviceId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

#### Update Service
```typescript
// PUT /api/v1/services/:id
async function updateService(token: string, serviceId: number, data: UpdateServiceRequest): Promise<ApiResponse<Service>> {
  const response = await fetch(`${API_BASE_URL}/services/${serviceId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  return response.json()
}
```

#### Delete Service
```typescript
// DELETE /api/v1/services/:id
async function deleteService(token: string, serviceId: number): Promise<ApiResponse<null>> {
  const response = await fetch(`${API_BASE_URL}/services/${serviceId}`, {
    method: 'DELETE',
    headers: { 'Authorization': `Bearer ${token}` }
  })
  return response.json()
}
```

## Next.js Integration Examples

### API Service Class
```typescript
class OpsAdminApiService {
  private baseUrl: string
  private token: string | null = null

  constructor(baseUrl: string = 'http://localhost:8080/api/v1') {
    this.baseUrl = baseUrl
  }

  setToken(token: string) {
    this.token = token
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = endpoint.startsWith('/') ? `${this.baseUrl}${endpoint}` : endpoint
    const headers = {
      'Content-Type': 'application/json',
      ...(this.token && { 'Authorization': `Bearer ${this.token}` }),
      ...options.headers
    }

    const response = await fetch(url, {
      ...options,
      headers
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Authentication methods
  async login(data: LoginRequest) {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(data)
    })
    if (response.status && response.data) {
      this.setToken(response.data.token)
    }
    return response
  }

  async register(data: RegisterRequest) {
    return this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // User methods
  async getUserProfile() {
    return this.request<User>('/users/profile')
  }

  async updateUserProfile(data: UpdateUserRequest) {
    return this.request<User>('/users/profile', {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  // Workspace methods
  async getWorkspaces() {
    return this.request<Workspace[]>('/workspaces')
  }

  async createWorkspace(data: CreateWorkspaceRequest) {
    return this.request<Workspace>('/workspaces', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // Job methods
  async getJobs() {
    return this.request<Job[]>('/jobs')
  }

  async createJob(data: CreateJobRequest) {
    return this.request<Job>('/jobs', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // Service methods
  async getServices(filters?: {
    namespace_id?: number
    deployment_id?: number
    name?: string
    type?: string
  }) {
    const searchParams = new URLSearchParams()

    if (filters?.namespace_id) {
      searchParams.append('namespace_id', filters.namespace_id.toString())
    }
    if (filters?.deployment_id) {
      searchParams.append('deployment_id', filters.deployment_id.toString())
    }
    if (filters?.name) {
      searchParams.append('name', filters.name)
    }
    if (filters?.type) {
      searchParams.append('type', filters.type)
    }

    const url = `/services${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
    return this.request<Service[]>(url)
  }

  async getService(serviceId: number) {
    return this.request<Service>(`/services/${serviceId}`)
  }

  async createService(data: CreateServiceRequest) {
    return this.request<Service>('/services', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async updateService(serviceId: number, data: UpdateServiceRequest) {
    return this.request<Service>(`/services/${serviceId}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  async deleteService(serviceId: number) {
    return this.request<null>(`/services/${serviceId}`, {
      method: 'DELETE'
    })
  }
}

export default OpsAdminApiService
```

### React Hook Example
```typescript
import { useState, useEffect } from 'react'
import OpsAdminApiService from './api-service'

export function useOpsAdminApi() {
  const [api] = useState(() => new OpsAdminApiService())
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const execute = async <T>(apiCall: () => Promise<ApiResponse<T>>): Promise<T | null> => {
    setLoading(true)
    setError(null)
    try {
      const response = await apiCall()
      if (response.status) {
        return response.data
      } else {
        setError(response.message)
        return null
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }

  return { api, execute, loading, error }
}
```

### Usage in Next.js Component
```typescript
'use client'
import { useState } from 'react'
import { useOpsAdminApi } from './hooks/useOpsAdminApi'

export default function WorkspacePage() {
  const { api, execute, loading, error } = useOpsAdminApi()
  const [workspaces, setWorkspaces] = useState<Workspace[]>([])

  const loadWorkspaces = async () => {
    const data = await execute(() => api.getWorkspaces())
    if (data) {
      setWorkspaces(data)
    }
  }

  const createWorkspace = async (formData: CreateWorkspaceRequest) => {
    const newWorkspace = await execute(() => api.createWorkspace(formData))
    if (newWorkspace) {
      setWorkspaces(prev => [...prev, newWorkspace])
    }
  }

  return (
    <div>
      {loading && <div>Loading...</div>}
      {error && <div>Error: {error}</div>}
      {/* Your UI components */}
    </div>
  )
}
```

## Environment Variables
```env
# .env.local
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1
NEXT_PUBLIC_API_TIMEOUT=10000
```

## Error Handling
All API responses follow the standard format. Handle errors consistently:

```typescript
if (!response.status) {
  // Handle API error
  console.error('API Error:', response.message)
  throw new Error(response.message)
}
```

## CORS Configuration
The API is configured with CORS allowing:
- Origins: `*`
- Methods: `GET, POST, PUT, DELETE, OPTIONS`
- Headers: `Origin, Content-Type, Accept, Authorization`
