'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { Plus, Trash2, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';

import { MainHeader } from '@/components/main/layout/main-header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useClusterStore } from '@/store/cluster/action';
import { useProjectStore } from '@/store/project/action';
import { CreateProjectRequest } from '@/store/project/type';
import { useWorkspaceStore } from '@/store/workspace/action';

// Validation schema
const createProjectSchema = z.object({
  name: z
    .string()
    .min(1, 'Project name is required')
    .regex(
      /^[a-z0-9-]+$/,
      'Name must contain only lowercase letters, numbers, and hyphens'
    ),
  slug: z
    .string()
    .min(1, 'Project slug is required')
    .regex(
      /^[a-z0-9-]+$/,
      'Slug must contain only lowercase letters, numbers, and hyphens'
    ),
  is_active: z.literal(true),
  type: z.enum(['template', 'draft', 'project']),
  cluster_id: z.number().min(1, 'Cluster is required'),
  deployments: z
    .array(
      z.object({
        name: z
          .string()
          .min(1, 'Deployment name is required')
          .regex(
            /^[a-z0-9-]+$/,
            'Deployment name must contain only lowercase letters, numbers, and hyphens'
          ),
        image: z
          .string()
          .min(1, 'Image is required')
          .regex(
            /^[a-z0-9\-_\.:\/]+$/,
            'Image must contain only lowercase letters, numbers, and valid Docker image characters'
          ),
        container_port: z.number().min(1, 'Container port is required'),
        replicas: z.number().min(1, 'Replicas must be at least 1'),
        environments: z.array(
          z.object({
            name: z.string().min(1, 'Environment name is required'),
            value: z.string().min(1, 'Environment value is required'),
          })
        ),
        services: z.array(
          z.object({
            name: z
              .string()
              .min(1, 'Service name is required')
              .regex(
                /^[a-z0-9-]+$/,
                'Service name must contain only lowercase letters, numbers, and hyphens'
              ),
            port: z.string().min(1, 'Port is required'),
            target_port: z.string().min(1, 'Target port is required'),
            type: z.string().min(1, 'Service type is required'),
          })
        ),
      })
    )
    .min(1, 'At least one deployment is required'),
});

type CreateProjectForm = z.infer<typeof createProjectSchema>;

export default function CreateProjectPage() {
  const router = useRouter();
  const { createProject, creating } = useProjectStore();
  const { clusters, fetchClusters } = useClusterStore();
  const { selectedWorkspace } = useWorkspaceStore();
  const [isClient, setIsClient] = useState(false);

  const form = useForm<CreateProjectForm>({
    resolver: zodResolver(createProjectSchema),
    defaultValues: {
      name: '',
      slug: '',
      is_active: true,
      type: 'draft',
      cluster_id: 0,
      deployments: [
        {
          name: '',
          image: '',
          container_port: 80,
          replicas: 1,
          environments: [{ name: '', value: '' }],
          services: [
            {
              name: '',
              port: '',
              target_port: '',
              type: 'ClusterIP',
            },
          ],
        },
      ],
    },
  });

  const {
    fields: deploymentFields,
    append: appendDeployment,
    remove: removeDeployment,
  } = useFieldArray({
    control: form.control,
    name: 'deployments',
  });

  useEffect(() => {
    setIsClient(true);
    // Fetch clusters with the selected workspace ID
    if (selectedWorkspace?.id) {
      fetchClusters(selectedWorkspace.id);
    } else {
      fetchClusters();
    }
  }, [fetchClusters, selectedWorkspace?.id]);

  const onSubmit = async (data: CreateProjectForm) => {
    try {
      await createProject(data as CreateProjectRequest);
      router.push('/projects');
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  };

  const handleNameChange = (value: string) => {
    form.setValue('name', value);
    if (!form.watch('slug')) {
      form.setValue('slug', generateSlug(value));
    }
  };

  if (!isClient) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='h-96 bg-muted animate-pulse rounded-md flex items-center justify-center'>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col'>
      <MainHeader />
      <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
        <div className='flex items-center gap-4 mb-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.push('/projects')}
            className='flex items-center gap-2'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <div>
            <h1 className='text-3xl font-medium tracking-tight'>
              Create Project
            </h1>
            <p className='text-muted-foreground'>
              Create a new project with deployments and services
            </p>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            {/* Project Details */}
            <Card>
              <CardHeader>
                <CardTitle>Project Details</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <FormField
                    control={form.control}
                    name='name'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder='my-app'
                            {...field}
                            onChange={e => handleNameChange(e.target.value)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='slug'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Slug</FormLabel>
                        <FormControl>
                          <Input placeholder='my-app-prod' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='type'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select project type' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value='template'>Template</SelectItem>
                            <SelectItem value='draft'>Draft</SelectItem>
                            <SelectItem value='project'>Project</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='cluster_id'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cluster</FormLabel>
                        <Select
                          onValueChange={value =>
                            field.onChange(parseInt(value))
                          }
                          value={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select cluster' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {clusters.map(cluster => (
                              <SelectItem
                                key={cluster.id}
                                value={cluster.id.toString()}
                              >
                                {cluster.name} ({cluster.region})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Deployments */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle>Deployments</CardTitle>
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={() =>
                    appendDeployment({
                      name: '',
                      image: '',
                      container_port: 80,
                      replicas: 1,
                      environments: [{ name: '', value: '' }],
                      services: [
                        {
                          name: '',
                          port: '',
                          target_port: '',
                          type: 'ClusterIP',
                        },
                      ],
                    })
                  }
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4' />
                  Add Deployment
                </Button>
              </CardHeader>
              <CardContent className='space-y-6'>
                {deploymentFields.map((deployment, deploymentIndex) => (
                  <DeploymentForm
                    key={deployment.id}
                    form={form}
                    deploymentIndex={deploymentIndex}
                    onRemove={() => removeDeployment(deploymentIndex)}
                    canRemove={deploymentFields.length > 1}
                  />
                ))}
              </CardContent>
            </Card>

            {/* Submit Button */}
            <div className='flex justify-end gap-4'>
              <Button
                type='button'
                variant='outline'
                onClick={() => router.push('/projects')}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={creating}>
                {creating ? 'Creating...' : 'Create Project'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}

// Deployment Form Component
function DeploymentForm({
  form,
  deploymentIndex,
  onRemove,
  canRemove,
}: {
  form: any;
  deploymentIndex: number;
  onRemove: () => void;
  canRemove: boolean;
}) {
  const {
    fields: environmentFields,
    append: appendEnvironment,
    remove: removeEnvironment,
  } = useFieldArray({
    control: form.control,
    name: `deployments.${deploymentIndex}.environments`,
  });

  const {
    fields: serviceFields,
    append: appendService,
    remove: removeService,
  } = useFieldArray({
    control: form.control,
    name: `deployments.${deploymentIndex}.services`,
  });

  return (
    <div className='border rounded-lg p-4 space-y-4'>
      <div className='flex items-center justify-between'>
        <h3 className='text-lg font-medium'>
          Deployment {deploymentIndex + 1}
        </h3>
        {canRemove && (
          <Button
            type='button'
            variant='ghost'
            size='sm'
            onClick={onRemove}
            className='text-destructive hover:text-destructive'
          >
            <Trash2 className='h-4 w-4' />
          </Button>
        )}
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        <FormField
          control={form.control}
          name={`deployments.${deploymentIndex}.name`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Deployment Name</FormLabel>
              <FormControl>
                <Input placeholder='web-app' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`deployments.${deploymentIndex}.image`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Docker Image</FormLabel>
              <FormControl>
                <Input placeholder='nginx:latest' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`deployments.${deploymentIndex}.container_port`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Container Port</FormLabel>
              <FormControl>
                <Input
                  type='number'
                  placeholder='80'
                  {...field}
                  onChange={e => field.onChange(parseInt(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`deployments.${deploymentIndex}.replicas`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Replicas</FormLabel>
              <FormControl>
                <Input
                  type='number'
                  placeholder='3'
                  {...field}
                  onChange={e => field.onChange(parseInt(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Environment Variables */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <Label>Environment Variables</Label>
          <Button
            type='button'
            variant='outline'
            size='sm'
            onClick={() => appendEnvironment({ name: '', value: '' })}
            className='flex items-center gap-2'
          >
            <Plus className='h-4 w-4' />
            Add Environment
          </Button>
        </div>
        {environmentFields.map((env, envIndex) => (
          <div key={env.id} className='flex gap-2 items-start'>
            <FormField
              control={form.control}
              name={`deployments.${deploymentIndex}.environments.${envIndex}.name`}
              render={({ field }) => (
                <FormItem className='flex-1'>
                  <FormControl>
                    <Input placeholder='ENV' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name={`deployments.${deploymentIndex}.environments.${envIndex}.value`}
              render={({ field }) => (
                <FormItem className='flex-1'>
                  <FormControl>
                    <Input placeholder='production' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {environmentFields.length > 1 && (
              <Button
                type='button'
                variant='ghost'
                size='sm'
                onClick={() => removeEnvironment(envIndex)}
                className='text-destructive hover:text-destructive'
              >
                <Trash2 className='h-4 w-4' />
              </Button>
            )}
          </div>
        ))}
      </div>

      {/* Services */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <Label>Services</Label>
          <Button
            type='button'
            variant='outline'
            size='sm'
            disabled={true}
            onClick={() =>
              appendService({
                name: '',
                port: '',
                target_port: '',
                type: 'ClusterIP',
              })
            }
            className=' items-center gap-2 hidden'
          >
            <Plus className='h-4 w-4' />
            Add Service
          </Button>
        </div>
        {serviceFields.map((service, serviceIndex) => (
          <ServiceForm
            key={service.id}
            form={form}
            deploymentIndex={deploymentIndex}
            serviceIndex={serviceIndex}
            onRemove={() => removeService(serviceIndex)}
            canRemove={serviceFields.length > 1}
          />
        ))}
      </div>
    </div>
  );
}

// Service Form Component
function ServiceForm({
  form,
  deploymentIndex,
  serviceIndex,
  onRemove,
  canRemove,
}: {
  form: any;
  deploymentIndex: number;
  serviceIndex: number;
  onRemove: () => void;
  canRemove: boolean;
}) {
  return (
    <div className='border rounded p-3 space-y-3'>
      <div className='flex items-center justify-between'>
        <Label className='text-sm font-medium'>
          Service {serviceIndex + 1}
        </Label>
        {canRemove && (
          <Button
            type='button'
            variant='ghost'
            size='sm'
            onClick={onRemove}
            className='text-destructive hover:text-destructive'
          >
            <Trash2 className='h-4 w-4' />
          </Button>
        )}
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
        <FormField
          control={form.control}
          name={`deployments.${deploymentIndex}.services.${serviceIndex}.name`}
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm'>Service Name</FormLabel>
              <FormControl>
                <Input placeholder='web-service' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`deployments.${deploymentIndex}.services.${serviceIndex}.type`}
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm'>Service Type</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder='Select type' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value='ClusterIP'>ClusterIP</SelectItem>
                  <SelectItem value='NodePort'>NodePort</SelectItem>
                  <SelectItem value='LoadBalancer'>LoadBalancer</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`deployments.${deploymentIndex}.services.${serviceIndex}.port`}
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm'>Port</FormLabel>
              <FormControl>
                <Input placeholder='80' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`deployments.${deploymentIndex}.services.${serviceIndex}.target_port`}
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm'>Target Port</FormLabel>
              <FormControl>
                <Input placeholder='80' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
