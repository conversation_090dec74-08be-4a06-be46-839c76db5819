import { MainHeader } from '@/components/main/layout/main-header';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export default function AdminPanel() {
  return (
    <div className='flex flex-1 flex-col'>
      {/* Header */}
      <MainHeader />

      {/* Main Content */}
      <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
        <div className='grid auto-rows-min gap-4 md:grid-cols-3'>
          <Card>
            <CardHeader className='pb-2'>
              <CardDescription>Total Users</CardDescription>
              <CardTitle className='text-4xl'>1,234</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-xs text-muted-foreground'>
                +20.1% from last month
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='pb-2'>
              <CardDescription>Active Sessions</CardDescription>
              <CardTitle className='text-4xl'>456</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-xs text-muted-foreground'>
                +12.5% from last hour
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='pb-2'>
              <CardDescription>Revenue</CardDescription>
              <CardTitle className='text-4xl'>$12,345</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-xs text-muted-foreground'>
                +8.2% from last month
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-7'>
          <Card className='col-span-4'>
            <CardHeader>
              <CardTitle>Overview</CardTitle>
            </CardHeader>
            <CardContent className='pl-2'>
              <div className='h-[200px] bg-muted/50 rounded-xl flex items-center justify-center'>
                <p className='text-muted-foreground'>Chart placeholder</p>
              </div>
            </CardContent>
          </Card>

          <Card className='col-span-3'>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest user activities and system events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {[1, 2, 3, 4, 5].map(item => (
                  <div key={item} className='flex items-center space-x-4'>
                    <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
                    <div className='flex-1 space-y-1'>
                      <p className='text-sm font-medium leading-none'>
                        User action {item}
                      </p>
                      <p className='text-sm text-muted-foreground'>
                        {item} minutes ago
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* User Profile Section */}
        <div className='grid gap-4 md:grid-cols-2'>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common administrative tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-2'>
                <button className='w-full text-left p-2 rounded-md hover:bg-muted transition-colors'>
                  Manage Users
                </button>
                <button className='w-full text-left p-2 rounded-md hover:bg-muted transition-colors'>
                  View Reports
                </button>
                <button className='w-full text-left p-2 rounded-md hover:bg-muted transition-colors'>
                  System Settings
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
