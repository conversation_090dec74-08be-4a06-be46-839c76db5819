import './globals.css';
import { Metadata } from 'next';
import { ReactNode } from 'react';

import { StyledComponentsProvider } from '@/components/providers/StyledComponentsProvider';
import { ThemeProvider } from '@/components/providers/ThemeProvider';
import { lineSeedSans } from '@/fonts/LineSeedSans';

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body className={`${lineSeedSans.className} antialiased`}>
        <ThemeProvider
          attribute='class'
          defaultTheme='system'
          enableSystem
          disableTransitionOnChange
        >
          <StyledComponentsProvider>{children}</StyledComponentsProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
