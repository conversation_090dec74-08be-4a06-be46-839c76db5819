'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Plus, Trash2, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  ModalResponsive,
  ModalResponsiveContent,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveTrigger,
} from '@/components/ui/modal-responsive';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useAuthStore } from '@/store/auth/action';
import { useOrderStore } from '@/store/order/action';
import { useTemplateStore } from '@/store/template/action';

const createOrderSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .refine(val => val === val.toLowerCase(), 'Name must be lowercase only'),
  description: z.string().optional(),
  template_id: z.number().min(1, 'Template is required'),
  order_domains: z
    .array(
      z.object({
        name: z
          .string()
          .min(1, 'Domain name is required')
          .refine(
            val => val === val.toLowerCase(),
            'Domain name must be lowercase only'
          ),
      })
    )
    .min(1, 'At least one domain is required'),
});

type CreateOrderForm = z.infer<typeof createOrderSchema>;

interface CreateOrderModalProps {
  children: React.ReactNode;
  onSuccess?: () => void;
}

export function CreateOrderModal({
  children,
  onSuccess,
}: CreateOrderModalProps) {
  const [open, setOpen] = useState(false);
  const { me } = useAuthStore();
  const { createOrder, creating } = useOrderStore();
  const {
    templates,
    fetchTemplates,
    loading: templatesLoading,
  } = useTemplateStore();

  const form = useForm<CreateOrderForm>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      name: '',
      description: '',
      template_id: 0,
      order_domains: [{ name: '' }],
    },
  });

  const {
    fields: domainFields,
    append: appendDomain,
    remove: removeDomain,
  } = useFieldArray({
    control: form.control,
    name: 'order_domains',
  });

  useEffect(() => {
    if (open && templates.length === 0) {
      fetchTemplates();
    }
  }, [open, templates.length, fetchTemplates]);

  const generateOrderCode = (templateName: string, userId: number): string => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`;
    return `${templateName.toUpperCase()}-${userId}-${timestamp}`;
  };

  const onSubmit = async (data: CreateOrderForm) => {
    try {
      if (!me?.id) {
        toast.error('User information not available');
        return;
      }

      const selectedTemplate = templates.find(t => t.id === data.template_id);
      if (!selectedTemplate) {
        toast.error('Selected template not found');
        return;
      }

      const generatedCode = generateOrderCode(selectedTemplate.name, me.id);

      const orderData = {
        ...data,
        code: generatedCode,
        description: data.description || '',
      };

      const response = await createOrder(orderData);
      if (response?.status) {
        toast.success('Order created successfully');
        form.reset();
        setOpen(false);
        onSuccess?.();
      } else {
        toast.error(response?.message || 'Failed to create order');
      }
    } catch (error) {
      console.error('Failed to create order:', error);
      toast.error('Failed to create order');
    }
  };

  const handleAddDomain = () => {
    appendDomain({ name: '' });
  };

  const handleRemoveDomain = (index: number) => {
    if (domainFields.length > 1) {
      removeDomain(index);
    }
  };

  return (
    <ModalResponsive open={open} onOpenChange={setOpen}>
      <ModalResponsiveTrigger asChild>{children}</ModalResponsiveTrigger>
      <ModalResponsiveContent className='max-w-2xl'>
        <ModalResponsiveHeader>
          <ModalResponsiveTitle>Create New Order</ModalResponsiveTitle>
          <ModalResponsiveDescription>
            Fill in the details to create a new order.
          </ModalResponsiveDescription>
        </ModalResponsiveHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter order name' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Enter order description'
                      className='min-h-[80px]'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='template_id'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Template</FormLabel>
                  <Select
                    onValueChange={value => field.onChange(parseInt(value, 10))}
                    value={field.value ? field.value.toString() : ''}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select a template' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {templatesLoading ? (
                        <SelectItem value='loading' disabled>
                          Loading templates...
                        </SelectItem>
                      ) : templates.length === 0 ? (
                        <SelectItem value='no-templates' disabled>
                          No templates available
                        </SelectItem>
                      ) : (
                        templates.map(template => (
                          <SelectItem
                            key={template.id}
                            value={template.id.toString()}
                          >
                            {template.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <FormLabel>Order Domains</FormLabel>
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={handleAddDomain}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4' />
                  Add Domain
                </Button>
              </div>

              <div className='space-y-3'>
                {domainFields.map((field, index) => (
                  <div key={field.id} className='flex items-end gap-2'>
                    <FormField
                      control={form.control}
                      name={`order_domains.${index}.name`}
                      render={({ field }) => (
                        <FormItem className='flex-1'>
                          <FormControl>
                            <Input
                              placeholder='Enter domain name (e.g., example.com)'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {domainFields.length > 1 && (
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={() => handleRemoveDomain(index)}
                        className='flex items-center gap-1 text-destructive hover:text-destructive'
                      >
                        <Trash2 className='h-4 w-4' />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <ModalResponsiveFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => setOpen(false)}
                disabled={creating}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={creating}>
                {creating ? (
                  <>
                    <Loader2 className='h-4 w-4 animate-spin mr-2' />
                    Creating...
                  </>
                ) : (
                  'Create Order'
                )}
              </Button>
            </ModalResponsiveFooter>
          </form>
        </Form>
      </ModalResponsiveContent>
    </ModalResponsive>
  );
}
