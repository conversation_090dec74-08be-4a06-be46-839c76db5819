'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  ModalResponsive,
  ModalResponsiveContent,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveTrigger,
} from '@/components/ui/modal-responsive';
import { useOrderStore } from '@/store/order/action';

const addOrderDomainSchema = z.object({
  name: z.string().min(1, 'Domain name is required'),
});

type AddOrderDomainForm = z.infer<typeof addOrderDomainSchema>;

interface AddOrderDomainModalProps {
  children: React.ReactNode;
  orderId: number;
  onSuccess?: () => void;
}

export function AddOrderDomainModal({
  children,
  orderId,
  onSuccess,
}: AddOrderDomainModalProps) {
  const [open, setOpen] = useState(false);
  const { createOrderDomain, creatingOrderDomain } = useOrderStore();

  const form = useForm<AddOrderDomainForm>({
    resolver: zodResolver(addOrderDomainSchema),
    defaultValues: {
      name: '',
    },
  });

  const onSubmit = async (data: AddOrderDomainForm) => {
    try {
      const response = await createOrderDomain({
        name: data.name,
        is_available: false, // Fixed value
        order_id: orderId,
      });

      if (response?.status) {
        toast.success('Order domain added successfully');
        form.reset();
        setOpen(false);
        onSuccess?.();
      } else {
        toast.error(response?.message || 'Failed to add order domain');
      }
    } catch (error: any) {
      console.error('Failed to add order domain:', error);
      toast.error(
        error?.response?.data?.message || 'Failed to add order domain'
      );
    }
  };

  return (
    <ModalResponsive open={open} onOpenChange={setOpen}>
      <ModalResponsiveTrigger asChild>{children}</ModalResponsiveTrigger>
      <ModalResponsiveContent className='max-w-md'>
        <ModalResponsiveHeader>
          <ModalResponsiveTitle>Add Order Domain</ModalResponsiveTitle>
          <ModalResponsiveDescription>
            Add a new domain to this order.
          </ModalResponsiveDescription>
        </ModalResponsiveHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Domain Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter domain name (e.g., example.com)'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='text-sm text-muted-foreground p-3 bg-muted/50 rounded-lg'>
              <p>
                New domains will be created as <strong>unavailable</strong> by
                default.
              </p>
            </div>

            <ModalResponsiveFooter className='flex flex-col-reverse gap-3 sm:flex-row sm:justify-end sm:gap-2'>
              <Button
                type='button'
                variant='outline'
                onClick={() => setOpen(false)}
                disabled={creatingOrderDomain}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={creatingOrderDomain}>
                {creatingOrderDomain ? (
                  <>
                    <Plus className='h-4 w-4 animate-spin' />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className='h-4 w-4' />
                    Add Domain
                  </>
                )}
              </Button>
            </ModalResponsiveFooter>
          </form>
        </Form>
      </ModalResponsiveContent>
    </ModalResponsive>
  );
}
