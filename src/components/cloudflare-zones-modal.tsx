'use client';

import {
  Cloud,
  Globe,
  Building,
  Hash,
  CheckCircle,
  XCircle,
  Loader2,
  AlertCircle,
  RefreshCw,
  Plus,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { getDomains } from '@/actions/domain';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ModalResponsive,
  ModalResponsiveClose,
  ModalResponsiveContent,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
} from '@/components/ui/modal-responsive';
import { Skeleton } from '@/components/ui/skeleton';
import { useCloudflareStore } from '@/store/cloudflare/action';
import { useDomainStore } from '@/store/domain/action';

interface CloudflareZonesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: number;
  onDomainCreated?: () => void;
}

export function CloudflareZonesModal({
  open,
  onOpenChange,
  projectId,
  onDomainCreated,
}: CloudflareZonesModalProps) {
  const { zones, loading, error, fetchCloudflareZones, clearError } =
    useCloudflareStore();
  const { createDomain } = useDomainStore();

  // Track which zone is being processed for domain creation
  const [creatingDomainForZone, setCreatingDomainForZone] = useState<
    string | null
  >(null);
  const [validatingDomainForZone, setValidatingDomainForZone] = useState<
    string | null
  >(null);

  // Fetch zones when modal opens
  useEffect(() => {
    if (open && !zones && !loading) {
      fetchCloudflareZones();
    }
  }, [open, zones, loading, fetchCloudflareZones]);

  // Validation function to check if zone can have domain added
  const canAddDomainToZone = (zoneName: string, zoneStatus: string) => {
    const masterDomain = 'kabao.dev'; // From MASTER_DOMAIN environment variable
    return zoneStatus === 'active' && zoneName !== masterDomain;
  };

  const handleAddDomainToZone = async (zone: any) => {
    if (!canAddDomainToZone(zone.zone_name, zone.zone_status)) {
      return;
    }

    setValidatingDomainForZone(zone.zone_id);

    try {
      // Step 1: Validate that domain doesn't already exist
      const validationResponse: any = await getDomains({
        name: zone.zone_name,
      });

      // Check if domain already exists
      if (
        validationResponse?.status &&
        validationResponse.data &&
        Array.isArray(validationResponse.data) &&
        validationResponse.data.length > 0
      ) {
        toast.error(
          `Domain '${zone.zone_name}' already exists in this project`
        );
        return;
      }

      // Step 2: Create domain if validation passes
      setValidatingDomainForZone(null);
      setCreatingDomainForZone(zone.zone_id);

      const domainData = {
        name: zone.zone_name,
        is_default: false,
        is_active: zone.zone_status === 'active',
        zone_id: zone.zone_id,
        account_id: zone.account.id,
        account_name: zone.account.name,
        namespace_id: projectId,
      };

      const response = await createDomain(domainData);

      if (response?.status) {
        toast.success(`Domain "${zone.zone_name}" added successfully`);
        onDomainCreated?.();
        onOpenChange(false);
      } else {
        toast.error(response?.message || 'Failed to add domain');
      }
    } catch (error: any) {
      console.error('Error adding domain:', error);

      // Provide specific error messages
      if (
        error?.message?.includes('already exists') ||
        error?.message?.includes('duplicate')
      ) {
        toast.error(
          `Domain '${zone.zone_name}' already exists in this project`
        );
      } else {
        toast.error(error?.message || 'Failed to add domain');
      }
    } finally {
      setValidatingDomainForZone(null);
      setCreatingDomainForZone(null);
    }
  };

  const handleRetry = () => {
    clearError();
    fetchCloudflareZones();
  };

  const handleClose = () => {
    clearError();
    setCreatingDomainForZone(null);
    setValidatingDomainForZone(null);
    onOpenChange(false);
  };

  const ZoneSkeleton = () => (
    <Card>
      <CardHeader className='pb-3'>
        <div className='flex items-center justify-between'>
          <Skeleton className='h-5 w-48' />
          <Skeleton className='h-6 w-16' />
        </div>
      </CardHeader>
      <CardContent className='space-y-2'>
        <div className='flex items-center gap-2'>
          <Skeleton className='h-4 w-4' />
          <Skeleton className='h-4 w-32' />
        </div>
        <div className='flex items-center gap-2'>
          <Skeleton className='h-4 w-4' />
          <Skeleton className='h-4 w-24' />
        </div>
      </CardContent>
    </Card>
  );

  return (
    <>
      <style jsx>{`
        .minimal-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .minimal-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        .minimal-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(156, 163, 175, 0.5);
          border-radius: 3px;
        }
        .minimal-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(156, 163, 175, 0.7);
        }
        .dark .minimal-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(75, 85, 99, 0.5);
        }
        .dark .minimal-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(75, 85, 99, 0.7);
        }
      `}</style>
      <ModalResponsive open={open} onOpenChange={onOpenChange}>
        <ModalResponsiveContent className='sm:max-w-4xl max-h-[90vh] flex flex-col'>
          <ModalResponsiveHeader className='flex-shrink-0'>
            <div className='flex items-center gap-3'>
              <div className='flex h-10 w-10 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20'>
                <Cloud className='h-5 w-5 text-orange-600' />
              </div>
              <div>
                <ModalResponsiveTitle>Cloudflare Zones</ModalResponsiveTitle>
                <ModalResponsiveDescription>
                  View available Cloudflare zones and their DNS records count
                </ModalResponsiveDescription>
              </div>
            </div>
          </ModalResponsiveHeader>

          <div
            className='flex-1 overflow-y-auto space-y-6 min-h-0 minimal-scrollbar'
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(156, 163, 175, 0.5) transparent',
            }}
          >
            {/* Summary Section */}
            {zones?.data?.summary && (
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <Card>
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm font-medium flex items-center gap-2'>
                      <Globe className='h-4 w-4' />
                      Total Domains
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='text-2xl font-bold'>
                      {zones.data.summary.total_domains}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm font-medium flex items-center gap-2'>
                      <Hash className='h-4 w-4' />
                      Zones with Records
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='text-2xl font-bold'>
                      {
                        Object.keys(zones.data.summary.zones_with_record_counts)
                          .length
                      }
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Loading State */}
            {loading && (
              <div className='space-y-4'>
                <div className='flex items-center justify-center py-8'>
                  <div className='flex items-center gap-2'>
                    <Loader2 className='h-5 w-5 animate-spin' />
                    <span>Loading Cloudflare zones...</span>
                  </div>
                </div>
                <div className='grid gap-4'>
                  {Array.from({ length: 3 }).map((_, index) => (
                    <ZoneSkeleton key={index} />
                  ))}
                </div>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className='flex flex-col items-center justify-center py-12 space-y-4'>
                <div className='flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20'>
                  <AlertCircle className='h-6 w-6 text-red-600' />
                </div>
                <div className='text-center'>
                  <h3 className='text-lg font-semibold'>
                    Failed to load zones
                  </h3>
                  <p className='text-muted-foreground mt-1'>{error}</p>
                </div>
                <Button onClick={handleRetry} variant='outline'>
                  <RefreshCw className='h-4 w-4 mr-2' />
                  Try Again
                </Button>
              </div>
            )}

            {/* Zones List */}
            {zones?.data?.zones_with_dns_records && !loading && !error && (
              <div className='space-y-4'>
                <h3 className='text-lg font-semibold'>Available Zones</h3>
                <div className='grid gap-4'>
                  {zones.data.zones_with_dns_records.map(zone => (
                    <Card
                      key={zone.zone_id}
                      className='hover:shadow-md transition-shadow'
                    >
                      <CardHeader className='pb-3'>
                        <div className='flex items-center justify-between'>
                          <CardTitle className='text-base font-medium flex items-center gap-2'>
                            <Globe className='h-4 w-4' />
                            {zone.zone_name}
                          </CardTitle>
                          <Badge
                            variant={
                              zone.zone_status === 'active'
                                ? 'default'
                                : 'secondary'
                            }
                          >
                            {zone.zone_status === 'active' ? (
                              <CheckCircle className='h-3 w-3 mr-1' />
                            ) : (
                              <XCircle className='h-3 w-3 mr-1' />
                            )}
                            {zone.zone_status}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className='space-y-3'>
                        <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                          <Building className='h-4 w-4' />
                          <span>Account: {zone.account.name}</span>
                        </div>
                        <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                          <Hash className='h-4 w-4' />
                          <span>Zone ID: {zone.zone_id}</span>
                        </div>
                        <div className='flex items-center justify-between'>
                          <span className='text-sm font-medium'>
                            DNS Records: {zone.dns_records.length}
                          </span>
                          <Badge variant='outline'>
                            {zone.dns_records.length} records
                          </Badge>
                        </div>

                        {/* Add Domain to Zone Button */}
                        {canAddDomainToZone(
                          zone.zone_name,
                          zone.zone_status
                        ) && (
                          <div className='pt-2 border-t'>
                            <Button
                              onClick={() => handleAddDomainToZone(zone)}
                              disabled={
                                creatingDomainForZone === zone.zone_id ||
                                validatingDomainForZone === zone.zone_id
                              }
                              className='w-full'
                              size='sm'
                            >
                              {validatingDomainForZone === zone.zone_id ? (
                                <>
                                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                                  Validating Domain...
                                </>
                              ) : creatingDomainForZone === zone.zone_id ? (
                                <>
                                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                                  Adding Domain...
                                </>
                              ) : (
                                <>
                                  <Plus className='h-4 w-4 mr-2' />
                                  Add Domain to Zone
                                </>
                              )}
                            </Button>
                          </div>
                        )}

                        {/* Show reason why domain cannot be added */}
                        {!canAddDomainToZone(
                          zone.zone_name,
                          zone.zone_status
                        ) && (
                          <div className='pt-2 border-t'>
                            <div className='text-xs text-muted-foreground text-center'>
                              {zone.zone_status !== 'active'
                                ? 'Zone must be active to add domain'
                                : 'Master domain cannot be added'}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Empty State */}
            {zones?.data?.zones_with_dns_records?.length === 0 &&
              !loading &&
              !error && (
                <div className='flex flex-col items-center justify-center py-12 space-y-4'>
                  <div className='flex h-12 w-12 items-center justify-center rounded-full bg-muted'>
                    <Cloud className='h-6 w-6 text-muted-foreground' />
                  </div>
                  <div className='text-center'>
                    <h3 className='text-lg font-semibold'>No zones found</h3>
                    <p className='text-muted-foreground mt-1'>
                      No Cloudflare zones are available at the moment.
                    </p>
                  </div>
                </div>
              )}
          </div>

          <ModalResponsiveFooter className='flex-shrink-0'>
            <ModalResponsiveClose asChild>
              <Button variant='outline' onClick={handleClose}>
                Close
              </Button>
            </ModalResponsiveClose>
          </ModalResponsiveFooter>
        </ModalResponsiveContent>
      </ModalResponsive>
    </>
  );
}
