'use client';

import * as React from 'react';

import { NavMain } from '@/components/main/layout/nav-main';
import { NavProjects } from '@/components/main/layout/nav-projects';
import { NavSecondary } from '@/components/main/layout/nav-secondary';
import { NavUser } from '@/components/main/layout/nav-user';
import { WorkspaceSelector } from '@/components/main/layout/workspace-selector';
import { CreateWorkspaceModal } from '@/components/main/workspace/create-workspace-modal';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { navigationData } from '@/lib/navigation';
import { useAuthStore } from '@/store/auth/action';
import { useClusterStore } from '@/store/cluster/action';
import { useWorkspaceStore } from '@/store/workspace/action';
import type { WorkspaceType } from '@/store/workspace/type';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { me } = useAuthStore();
  const { fetchClusters } = useClusterStore();
  const {
    workspaces,
    selectedWorkspace,
    loading,
    fetchWorkspaces,
    setSelectedWorkspace,
  } = useWorkspaceStore();
  const [isCreateModalOpen, setIsCreateModalOpen] = React.useState(false);

  // Fetch workspaces on component mount
  React.useEffect(() => {
    fetchWorkspaces();
  }, [fetchWorkspaces]);

  // Create user object for NavUser component
  const currentUser = me
    ? {
        name: me.name,
        email: me.email,
        avatar: '/avatars/blank-profile.webp',
      }
    : navigationData.user; // Fallback to default user data

  const handleWorkspaceSelect = (workspace: WorkspaceType) => {
    setSelectedWorkspace(workspace);
    // Trigger cluster refetch with the new workspace ID
    fetchClusters(workspace.id);
  };

  const handleCreateWorkspace = () => {
    setIsCreateModalOpen(true);
  };

  // Transform API workspace data to match the component interface
  const transformedWorkspaces = workspaces.map(workspace => ({
    id: workspace.id.toString(),
    name: workspace.name,
    description: workspace.description,
  }));

  const transformedCurrentWorkspace = selectedWorkspace
    ? {
        id: selectedWorkspace.id.toString(),
        name: selectedWorkspace.name,
        description: selectedWorkspace.description,
      }
    : navigationData.currentWorkspace;

  return (
    <>
      <Sidebar variant='inset' {...props}>
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <WorkspaceSelector
                workspaces={transformedWorkspaces}
                currentWorkspace={transformedCurrentWorkspace}
                onWorkspaceSelect={workspace => {
                  // Find the original workspace data
                  const originalWorkspace = workspaces.find(
                    w => w.id.toString() === workspace.id
                  );
                  if (originalWorkspace) {
                    handleWorkspaceSelect(originalWorkspace);
                  }
                }}
                onCreateWorkspace={handleCreateWorkspace}
                loading={loading}
              />
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <NavMain items={navigationData.navMain} />
          <NavProjects projects={navigationData.projects} />
          <NavSecondary
            items={navigationData.navSecondary}
            className='mt-auto'
          />
        </SidebarContent>
        <SidebarFooter>
          <NavUser user={currentUser} />
        </SidebarFooter>
      </Sidebar>

      <CreateWorkspaceModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
      />
    </>
  );
}
