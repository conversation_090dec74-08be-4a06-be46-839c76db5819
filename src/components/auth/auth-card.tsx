import { ComponentProps } from 'react';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface AuthCardProps extends ComponentProps<'div'> {
  children: React.ReactNode;
}

export function AuthCard({ className, children, ...props }: AuthCardProps) {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center min-h-screen p-4',
        className
      )}
      {...props}
    >
      <div className='w-full max-w-md'>
        <Card className='border-0 shadow-2xl bg-white/95 backdrop-blur-sm'>
          <CardContent className='p-8'>
            <div className='flex flex-col gap-6'>{children}</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
