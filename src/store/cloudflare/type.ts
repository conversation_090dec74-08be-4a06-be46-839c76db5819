// Cloudflare Store State and Actions
export interface CloudflareStates {
  zones: CloudflareZonesResponse | null;
  loading: boolean;
  error: string | null;
}

export interface CloudflareActions {
  setZones: (zones: CloudflareZonesResponse | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  fetchCloudflareZones: () => Promise<void>;
  clearError: () => void;
}

// API Response Types
export interface CloudflareZonesResponse {
  status: boolean;
  message: string;
  data: CloudflareZonesData;
}

export interface CloudflareZonesData {
  summary: CloudflareZonesSummary;
  zones_with_dns_records: CloudflareZone[];
}

export interface CloudflareZonesSummary {
  total_domains: number;
  zones_with_record_counts: Record<string, number>;
}

export interface CloudflareZone {
  zone_id: string;
  zone_name: string;
  zone_status: string;
  account: CloudflareAccount;
  dns_records: CloudflareDnsRecord[];
}

export interface CloudflareAccount {
  id: string;
  name: string;
}

export interface CloudflareDnsRecord {
  id: string;
  name: string;
  type: string;
  content: string;
  proxied: boolean;
}
