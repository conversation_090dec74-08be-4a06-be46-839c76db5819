import {
  <PERSON><PERSON><PERSON>,
  LayoutDashboard,
  Folder,
  Server,
  type LucideIcon,
  ListOrdered,
} from 'lucide-react';

export interface NavItem {
  title: string;
  url: string;
  icon: LucideIcon;
  isActive?: boolean;
  items?: NavSubItem[];
}

export interface NavSubItem {
  title: string;
  url: string;
}

export interface ProjectItem {
  name: string;
  url: string;
  icon: LucideIcon;
}

export interface Workspace {
  id: string;
  name: string;
  description?: string;
}

export interface NavigationData {
  user: {
    name: string;
    email: string;
    avatar: string;
  };
  workspaces: Workspace[];
  currentWorkspace: Workspace;
  navMain: NavItem[];
  navSecondary: NavItem[];
  projects: ProjectItem[];
}

export interface BreadcrumbItem {
  title: string;
  url: string;
  isCurrent: boolean;
}

export function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [];

  // Add home/dashboard as first item
  breadcrumbs.push({
    title: 'Dashboard',
    url: '/dashboard',
    isCurrent: pathname === '/dashboard',
  });

  if (pathname === '/dashboard') {
    return breadcrumbs;
  }

  let currentPath = '';
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === segments.length - 1;

    // Capitalize first letter and replace hyphens with spaces
    const title = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    breadcrumbs.push({
      title,
      url: currentPath,
      isCurrent: isLast,
    });
  });

  return breadcrumbs;
}

export const navigationData: NavigationData = {
  user: {
    name: 'Admin User',
    email: '<EMAIL>',
    avatar: '/avatars/blank-profile.webp',
  },
  workspaces: [
    { id: '1', name: 'Production', description: 'Production environment' },
    { id: '2', name: 'Staging', description: 'Staging environment' },
    { id: '3', name: 'Development', description: 'Development environment' },
  ],
  currentWorkspace: {
    id: '1',
    name: 'Production',
    description: 'Production environment',
  },
  navMain: [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      title: 'Clusters',
      url: '/clusters',
      icon: Server,
    },
    {
      title: 'Projects',
      url: '/projects',
      icon: Folder,
    },
    {
      title: 'Orders',
      url: '/orders',
      icon: ListOrdered,
    },
    // {
    //   title: 'Config',
    //   url: '/config',
    //   icon: Cog,
    //   items: [
    //     { title: 'Deployments', url: '/config/deployments' },
    //     { title: 'Services', url: '/config/services' },
    //     { title: 'Ingress', url: '/config/ingress' },
    //   ],
    // },
  ],
  navSecondary: [
    // {
    //   title: 'Support',
    //   url: '#',
    //   icon: LifeBuoy,
    // },
    // {
    //   title: 'Feedback',
    //   url: '#',
    //   icon: Send,
    // },
  ],
  projects: [
    {
      name: 'Design Engineering',
      url: '#',
      icon: PieChart,
    },
    {
      name: 'Sales & Marketing',
      url: '#',
      icon: PieChart,
    },
    {
      name: 'Travel',
      url: '#',
      icon: PieChart,
    },
  ],
};
