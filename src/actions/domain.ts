'use server';

import Http from '@/lib/http';

export interface DomainQueryParams {
  name?: string;
  is_default?: boolean;
  is_active?: boolean;
  namespace_id?: number;
}

export interface CreateDomainRequest {
  name: string;
  is_default: boolean;
  is_active: boolean;
  zone_id: string;
  account_id: string;
  account_name: string;
  namespace_id: number;
}

export interface UpdateDomainStatusRequest {
  is_active: boolean;
}

export async function getDomains(params: DomainQueryParams) {
  try {
    const queryParams = new URLSearchParams();

    // namespace_id is required
    if (params.namespace_id) {
      queryParams.append('namespace_id', params.namespace_id.toString());
    }

    // Add optional filters
    if (params.name) {
      queryParams.append('name', params.name);
    }
    if (params.is_default !== undefined) {
      queryParams.append('is_default', params.is_default.toString());
    }
    if (params.is_active !== undefined) {
      queryParams.append('is_active', params.is_active.toString());
    }

    const url = `/domains?${queryParams.toString()}`;
    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function createDomain(data: CreateDomainRequest) {
  try {
    const response = await Http.post('/domains', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getDomain(id: number) {
  try {
    const response = await Http.get(`/domains/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateDomainStatus(
  id: number,
  data: UpdateDomainStatusRequest
) {
  try {
    const response = await Http.patch(`/domains/status/${id}`, data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function setDomainAsDefault(id: number) {
  try {
    const response = await Http.patch(`/domains/default/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function deleteDomain(id: number) {
  try {
    const response = await Http.delete(`/domains/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
